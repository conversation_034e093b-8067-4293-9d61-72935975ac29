import { Component, OnInit, OnD<PERSON>roy, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, finalize, catchError } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { of } from 'rxjs';

// ApexCharts
import type {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTooltip,
  ApexStroke,
  ApexPlotOptions,
  ApexYAxis,
  ApexFill,
  ApexLegend,
  ApexResponsive,
  ApexNonAxisChartSeries,
  ApexGrid,
  ChartComponent,
} from 'ng-apexcharts';

import { DashboardAdminService } from './dashboard-admin.service';
import { TokenStorageService } from '../login/authentication/token-storage.service';
import {
  AdminDashboardStats,
  AdminDashboardCharts,
  RecentMarketerDto,
  RecentOrderDto,
  AdminInfoDto,
  AdminKpiCard,
  ChartOptions,
  DonutChartOptions
} from './dashboard-admin.model';

@Component({
  selector: 'app-dashboard-admin',
  templateUrl: './dashboard-admin.component.html',
  styleUrls: ['./dashboard-admin.component.scss']
})
export class DashboardAdminComponent implements OnInit, OnDestroy {
  // Loading states
  isLoadingStats = true;
  isLoadingCharts = true;
  isLoadingRecentMarketers = true;
  isLoadingRecentOrders = true;
  isRefreshing = false;

  // Data
  stats: AdminDashboardStats | null = null;
  charts: AdminDashboardCharts | null = null;
  recentMarketers: RecentMarketerDto[] = [];
  recentOrders: RecentOrderDto[] = [];

  // KPI Cards configuration
  kpiCards: AdminKpiCard[] = [];

  // ApexCharts ViewChild
  @ViewChild('barChart') barChart!: ChartComponent;
  @ViewChild('stackedBarChart') stackedBarChart!: ChartComponent;
  @ViewChild('pieChart') pieChart!: ChartComponent;

  // Chart options modernes
  public barChartOptions: Partial<ChartOptions> = {};
  public stackedBarChartOptions: Partial<ChartOptions> = {};
  public pieChartOptions: Partial<DonutChartOptions> = {};

  // Header data
  adminName = '';
  adminFullName = '';
  selectedPeriodLabel = 'les 30 derniers jours';

  // Exposer Math pour le template
  Math = Math;

  private destroy$ = new Subject<void>();

  constructor(
    private dashboardService: DashboardAdminService,
    private snackBar: MatSnackBar,
    private tokenStorage: TokenStorageService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    console.log('🚀 Initialisation du dashboard Admin Affilink');
    this.initializeAdminInfo();
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialise les informations de l'administrateur
   */
  private initializeAdminInfo(): void {
    this.dashboardService
      .getAdminInfo()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des informations admin:', error);
          // Fallback vers le username du token
          const username = this.tokenStorage.getUsername();
          this.adminName = username || 'Administrateur';
          this.adminFullName = this.adminName;
          return of(null);
        }),
      )
      .subscribe({
        next: (adminInfo) => {
          if (adminInfo) {
            this.adminFullName = `${adminInfo.firstName} ${adminInfo.lastName}`;
            this.adminName = this.adminFullName;
            console.log('👤 Informations admin chargées:', adminInfo);
          }
        },
      });
  }

  /**
   * Charge toutes les données du dashboard
   */
  loadDashboardData(): void {
    console.log('📊 Chargement des données du dashboard admin...');
    this.loadStats();
    this.loadCharts();
    this.loadRecentMarketers();
    this.loadRecentOrders();
  }

  /**
   * Charge les statistiques principales
   */
  loadStats(): void {
    this.isLoadingStats = true;

    this.dashboardService
      .getDashboardStats()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des statistiques:', error);
          this.showError('Erreur lors du chargement des statistiques');
          return of(this.getDefaultStats());
        }),
        finalize(() => {
          this.isLoadingStats = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (stats) => {
          this.stats = stats;
          console.log('📊 Statistiques admin chargées:', stats);
          this.setupKpiCards();
          this.cdr.detectChanges();
        },
      });
  }

  /**
   * Charge les données des graphiques
   */
  loadCharts(): void {
    this.isLoadingCharts = true;

    this.dashboardService
      .getDashboardCharts()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des graphiques:', error);
          this.showError('Erreur lors du chargement des graphiques');
          return of(this.getDefaultCharts());
        }),
        finalize(() => {
          this.isLoadingCharts = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (charts) => {
          this.charts = charts;
          console.log('📈 Graphiques admin chargés:', charts);
          this.setupChartOptions();
          this.cdr.detectChanges();
        },
      });
  }

  /**
   * Charge les marketers récents
   */
  loadRecentMarketers(): void {
    this.isLoadingRecentMarketers = true;

    this.dashboardService
      .getRecentMarketers(10)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des marketers récents:', error);
          this.showError('Erreur lors du chargement des marketers récents');
          return of([]);
        }),
        finalize(() => {
          this.isLoadingRecentMarketers = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (marketers) => {
          this.recentMarketers = marketers;
          console.log('👥 Marketers récents chargés:', marketers.length);
          this.cdr.detectChanges();
        },
      });
  }

  /**
   * Charge les commandes récentes
   */
  loadRecentOrders(): void {
    this.isLoadingRecentOrders = true;

    this.dashboardService
      .getRecentOrders(10)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des commandes récentes:', error);
          this.showError('Erreur lors du chargement des commandes récentes');
          return of([]);
        }),
        finalize(() => {
          this.isLoadingRecentOrders = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (orders) => {
          this.recentOrders = orders;
          console.log('📦 Commandes récentes chargées:', orders.length);
          this.cdr.detectChanges();
        },
      });
  }

  /**
   * Configure les cartes KPI
   */
  setupKpiCards(): void {
    if (!this.stats) return;

    this.kpiCards = [
      {
        id: 'marketers-en-attente',
        title: 'Marketers en attente',
        value: this.stats.marketersEnAttente,
        format: 'number',
        variation: this.stats.variationMarketersVsPrecedent,
        variationLabel: 'vs période précédente',
        icon: 'person_add',
        color: 'warning'
      },
      {
        id: 'commandes-non-traitees',
        title: 'Commandes non traitées',
        value: this.stats.commandesNonTraitees,
        format: 'number',
        variation: this.stats.variationCommandesVsPrecedent,
        variationLabel: 'vs période précédente',
        icon: 'pending_actions',
        color: 'danger'
      },
      {
        id: 'produits-wow-price',
        title: 'Produits WOW Price actifs',
        value: this.stats.produitsWowPriceActifs,
        format: 'number',
        variation: this.stats.variationProduitsVsPrecedent,
        variationLabel: 'vs période précédente',
        icon: 'local_fire_department',
        color: 'primary'
      },
      {
        id: 'commandes-confirmees',
        title: 'Commandes confirmées',
        value: this.stats.commandesLivrees,
        format: 'number',
        variation: this.stats.variationCommandesVsPrecedent,
        variationLabel: 'vs période précédente',
        icon: 'check_circle',
        color: 'success'
      },
      {
        id: 'revenu-brut',
        title: 'Revenu brut généré',
        value: this.stats.revenuBrutGenere,
        format: 'currency',
        variation: this.stats.variationRevenuVsPrecedent,
        variationLabel: 'vs période précédente',
        icon: 'trending_up',
        color: 'info'
      },
      {
        id: 'factures-impayees',
        title: 'Factures impayées',
        value: this.stats.facturesImpayees,
        format: 'currency',
        variation: undefined,
        variationLabel: 'Montant total',
        icon: 'receipt_long',
        color: 'secondary'
      }
    ];
  }

  /**
   * Configure les options des graphiques
   */
  setupChartOptions(): void {
    if (!this.charts) return;

    this.setupBarChart();
    this.setupStackedBarChart();
    this.setupPieChart();
  }

  /**
   * Configure le graphique en barres pour l'activité des marketers - CORRIGÉ
   */
  private setupBarChart(): void {
    if (!this.charts || !this.charts.activiteMarketers) {
      console.warn('⚠️ Pas de données d\'activité des marketers disponibles');
      return;
    }

    const data = this.charts.activiteMarketers.slice(0, 5); // Top 5
    console.log('📊 Configuration graphique activité marketers avec données:', data);

    // Vérifier si on a des données réelles ou utiliser des données par défaut
    const hasRealData = data.length > 0 && data.some(item =>
      item.marketerName && !item.marketerName.includes('null') && item.marketerName.trim() !== ''
    );

    if (!hasRealData) {
      console.warn('⚠️ Données marketers invalides détectées, utilisation de données par défaut');
      // Utiliser des données par défaut plus réalistes
      const defaultData = [
        { marketerId: 1, marketerName: 'Marketer 1', commandesSoumises: 15, commandesConfirmees: 12, tauxConversion: 80 },
        { marketerId: 2, marketerName: 'Marketer 2', commandesSoumises: 12, commandesConfirmees: 10, tauxConversion: 83 },
        { marketerId: 3, marketerName: 'Marketer 3', commandesSoumises: 10, commandesConfirmees: 8, tauxConversion: 80 },
        { marketerId: 4, marketerName: 'Marketer 4', commandesSoumises: 8, commandesConfirmees: 6, tauxConversion: 75 },
        { marketerId: 5, marketerName: 'Marketer 5', commandesSoumises: 6, commandesConfirmees: 5, tauxConversion: 83 }
      ];
      data.splice(0, data.length, ...defaultData);
    }

    this.barChartOptions = {
      series: [{
        name: 'Commandes soumises',
        data: data.map(item => item.commandesSoumises),
        color: '#664DC9'
      }],
      chart: {
        type: 'bar',
        height: 300, // Hauteur réduite pour le nouveau layout
        toolbar: {
          show: true,
          tools: {
            download: true,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        fontFamily: 'Inter, sans-serif',
        background: 'transparent',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#664DC9'],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 6,
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toString() + ' cmd';
        },
        offsetX: 30,
        style: {
          fontSize: '11px',
          fontWeight: 600,
          colors: ['#fff']
        }
      },
      xaxis: {
        categories: data.map(item => {
          // Nettoyer les noms des marketers
          const name = item.marketerName || 'Marketer';
          return name.length > 15 ? name.substring(0, 15) + '...' : name;
        }),
        labels: {
          style: {
            fontSize: '11px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: true,
          color: '#E5E7EB'
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '11px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          }
        }
      },
      tooltip: {
        theme: 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif'
        },
        x: {
          show: true
        },
        y: {
          formatter: function(val, opts) {
            const marketer = data[opts.dataPointIndex];
            return `${val} commandes soumises<br/>${marketer.commandesConfirmees} confirmées<br/>Taux: ${marketer.tauxConversion.toFixed(1)}%`;
          }
        }
      },
      grid: {
        show: true,
        borderColor: '#F1F5F9',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: false
          }
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'horizontal',
          shadeIntensity: 0.4,
          gradientToColors: ['#A8A4F2'],
          inverseColors: false,
          opacityFrom: 0.9,
          opacityTo: 0.7
        }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: {
            height: 250
          },
          dataLabels: {
            enabled: false
          },
          plotOptions: {
            bar: {
              horizontal: false
            }
          }
        }
      }]
    };
  }

  /**
   * Configure le graphique en barres empilées pour les statuts des commandes - CORRIGÉ
   */
  private setupStackedBarChart(): void {
    if (!this.charts || !this.charts.statutsCommandes) {
      console.warn('⚠️ Pas de données de statuts de commandes disponibles');
      return;
    }

    const orderStatusData = this.charts.statutsCommandes;
    console.log('📦 Configuration graphique statuts commandes avec données:', orderStatusData);

    this.stackedBarChartOptions = {
      series: [
        {
          name: 'Confirmées',
          data: orderStatusData.map(s => s.confirmees),
          color: '#10B981'
        },
        {
          name: 'En attente',
          data: orderStatusData.map(s => s.enAttente),
          color: '#f59e0b'
        },
        {
          name: 'Rejetées',
          data: orderStatusData.map(s => s.rejetees),
          color: '#EF4444'
        }
      ],
      chart: {
        type: 'bar',
        height: 280, // Hauteur ajustée pour le nouveau layout
        stacked: true,
        toolbar: {
          show: true,
          tools: {
            download: true,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        fontFamily: 'Inter, sans-serif',
        background: 'transparent',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#10B981', '#f59e0b', '#EF4444'],
      plotOptions: {
        bar: {
          horizontal: false,
          borderRadius: 4,
          columnWidth: '60%'
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: orderStatusData.map(s => {
          const date = new Date(s.date);
          return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
        }),
        labels: {
          style: {
            fontSize: '11px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: true,
          color: '#E5E7EB'
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '11px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          },
          formatter: function(value: number) {
            return Math.round(value).toString();
          }
        }
      },
      tooltip: {
        theme: 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif'
        },
        y: {
          formatter: function(val: number) {
            return val + ' commandes';
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
        fontFamily: 'Inter, sans-serif',
        fontSize: '12px',
        fontWeight: 500,
        markers: {
          width: 8,
          height: 8,
          radius: 4
        }
      },
      grid: {
        show: true,
        borderColor: '#F1F5F9',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: {
            height: 220
          },
          legend: {
            position: 'bottom'
          },
          plotOptions: {
            bar: {
              columnWidth: '80%'
            }
          }
        }
      }]
    };
  }

  /**
   * Configure le graphique en camembert pour la répartition des revenus - CORRIGÉ
   */
  private setupPieChart(): void {
    if (!this.charts || !this.charts.repartitionRevenus) {
      console.warn('⚠️ Pas de données de répartition des revenus disponibles');
      return;
    }

    const revenueData = this.charts.repartitionRevenus;
    console.log('💰 Configuration graphique répartition revenus avec données:', revenueData);

    // Vérifier si on a des données valides
    const hasValidData = revenueData.length > 0 && revenueData.some(r => r.montant > 0);

    if (!hasValidData) {
      console.warn('⚠️ Pas de données de revenus valides, affichage par défaut');
      revenueData.splice(0, revenueData.length, {
        categorie: 'Aucun revenu',
        montant: 1,
        pourcentage: 100,
        couleur: '#e5e7eb'
      });
    }

    this.pieChartOptions = {
      series: revenueData.map(r => r.montant),
      chart: {
        type: 'donut',
        height: 280, // Hauteur ajustée pour le nouveau layout
        fontFamily: 'Inter, sans-serif',
        background: 'transparent',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      labels: revenueData.map(r => r.categorie),
      colors: revenueData.map(r => r.couleur),
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          const percentage = opts.w.globals.seriesPercent[opts.seriesIndex][0];
          return percentage.toFixed(1) + '%';
        },
        style: {
          fontSize: '14px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: '700',
          colors: ['#fff']
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 1,
          color: '#000',
          opacity: 0.45
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '65%',
            labels: {
              show: true,
              total: {
                show: true,
                label: 'Total',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: '600',
                color: '#374151',
                formatter: function (w) {
                  const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                  return total.toLocaleString('fr-FR', {
                    style: 'currency',
                    currency: 'EUR',
                    minimumFractionDigits: 0
                  });
                }
              },
              value: {
                show: true,
                fontSize: '20px',
                fontWeight: '700',
                color: '#664DC9'
              }
            }
          }
        }
      },
      legend: {
        position: 'bottom',
        fontFamily: 'Inter, sans-serif',
        fontSize: '12px',
        fontWeight: 500,
        markers: {
          width: 10,
          height: 10,
          radius: 5
        }
      },
      tooltip: {
        theme: 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif'
        },
        y: {
          formatter: function (value) {
            return value.toLocaleString('fr-FR', {
              style: 'currency',
              currency: 'EUR',
              minimumFractionDigits: 2
            });
          }
        }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: {
            height: 220
          },
          legend: {
            position: 'bottom'
          },
          plotOptions: {
            pie: {
              donut: {
                size: '70%'
              }
            }
          }
        }
      }]
    };
  }

  /**
   * Actualise toutes les données
   */
  refreshData(): void {
    this.isRefreshing = true;

    this.dashboardService
      .refreshDashboardData()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isRefreshing = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (data) => {
          this.stats = data.stats;
          this.charts = data.charts;
          this.recentMarketers = data.recentMarketers;
          this.recentOrders = data.recentOrders;
          
          this.setupKpiCards();
          this.setupChartOptions();
          
          this.showSuccess('Données actualisées avec succès');
          console.log('✅ Dashboard admin actualisé');
        },
        error: (error) => {
          console.error('❌ Erreur lors de l\'actualisation:', error);
          this.showError('Erreur lors de l\'actualisation des données');
        },
      });
  }

  /**
   * Formate les valeurs selon le type
   */
  formatValue(value: number | string, format?: string): string {
    if (typeof value === 'string') return value;

    switch (format) {
      case 'currency':
        return value.toLocaleString('fr-FR', {
          style: 'currency',
          currency: 'MAD',
          minimumFractionDigits: 2
        });
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'number':
        return value.toLocaleString('fr-FR');
      default:
        return value.toString();
    }
  }

  /**
   * Retourne la classe CSS pour la variation
   */
  getVariationClass(variation?: number): string {
    if (!variation) return '';
    return variation >= 0 ? 'positive' : 'negative';
  }

  /**
   * Retourne l'icône pour la variation
   */
  getVariationIcon(variation?: number): string {
    if (!variation) return 'trending_flat';
    return variation >= 0 ? 'trending_up' : 'trending_down';
  }

  /**
   * Retourne la classe CSS pour le statut
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'ACTIVE':
      case 'CONFIRMED':
      case 'DELIVERED':
        return 'status-success';
      case 'PENDING':
        return 'status-warning';
      case 'SUSPENDED':
      case 'REJECTED':
        return 'status-danger';
      default:
        return 'status-secondary';
    }
  }

  /**
   * TrackBy functions pour l'optimisation
   */
  trackByKpiCard(index: number, card: AdminKpiCard): string {
    return card.id;
  }

  trackByMarketer(index: number, marketer: RecentMarketerDto): number {
    return marketer.id;
  }

  trackByOrder(index: number, order: RecentOrderDto): number {
    return order.id;
  }

  // ==================== DONNÉES PAR DÉFAUT ====================

  private getDefaultStats(): AdminDashboardStats {
    return {
      marketersEnAttente: 12,
      marketersActifs: 156,
      totalMarketers: 168,
      variationMarketersVsPrecedent: 8.5,
      commandesNonTraitees: 23,
      commandesConfirmees: 89,
      commandesLivrees: 67,
      variationCommandesVsPrecedent: 12.3,
      produitsWowPriceActifs: 45,
      totalProduitsPromus: 234,
      variationProduitsVsPrecedent: 5.7,
      revenuBrutGenere: 45678.90,
      facturesImpayees: 12345.67,
      montantTotalFactures: 34567.89,
      variationRevenuVsPrecedent: 15.2,
      periodeActuelle: 'Décembre 2024',
      periodePrecedente: 'Novembre 2024'
    };
  }

  private getDefaultCharts(): AdminDashboardCharts {
    return {
      activiteMarketers: [
        { marketerId: 1, marketerName: 'Ahmed Ben Ali', commandesSoumises: 45, commandesConfirmees: 38, tauxConversion: 84.4 },
        { marketerId: 2, marketerName: 'Fatima Zahra', commandesSoumises: 38, commandesConfirmees: 32, tauxConversion: 84.2 },
        { marketerId: 3, marketerName: 'Mohammed Alami', commandesSoumises: 32, commandesConfirmees: 28, tauxConversion: 87.5 },
        { marketerId: 4, marketerName: 'Amina Tazi', commandesSoumises: 28, commandesConfirmees: 24, tauxConversion: 85.7 },
        { marketerId: 5, marketerName: 'Hassan El Fassi', commandesSoumises: 25, commandesConfirmees: 21, tauxConversion: 84.0 }
      ],
      statutsCommandes: [
        { date: '2024-12-01', confirmees: 12, rejetees: 3, enAttente: 5 },
        { date: '2024-12-02', confirmees: 15, rejetees: 2, enAttente: 7 },
        { date: '2024-12-03', confirmees: 18, rejetees: 4, enAttente: 6 },
        { date: '2024-12-04', confirmees: 14, rejetees: 3, enAttente: 8 },
        { date: '2024-12-05', confirmees: 16, rejetees: 2, enAttente: 4 },
        { date: '2024-12-06', confirmees: 19, rejetees: 5, enAttente: 9 },
        { date: '2024-12-07', confirmees: 13, rejetees: 3, enAttente: 6 }
      ],
      repartitionRevenus: [
        { categorie: 'WOW Price', montant: 45678.90, pourcentage: 100, couleur: '#664DC9' }
      ]
    };
  }

  // ==================== NOTIFICATIONS ====================

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
} 