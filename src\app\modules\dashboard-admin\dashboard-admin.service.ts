import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from "../../../environments/environment";

import {
  AdminDashboardStats,
  AdminDashboardCharts,
  RecentMarketerDto,
  RecentOrderDto,
  AdminInfoDto,
  MarketerActivityDto,
  OrderStatusDto,
  RevenueCategoryDto
} from './dashboard-admin.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardAdminService {

  private readonly API_URL = environment.baseUrl;

  constructor(private http: HttpClient) { }

  /**
   * Récupère les statistiques principales du dashboard admin
   */
  getDashboardStats(): Observable<AdminDashboardStats> {
    return this.http.get<AdminDashboardStats>(`${this.API_URL}/api/admin/dashboard/stats`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des statistiques admin:', error);
          return of(this.getDefaultStats());
        })
      );
  }

  /**
   * Récupère les données pour les graphiques
   */
  getDashboardCharts(): Observable<AdminDashboardCharts> {
    return this.http.get<AdminDashboardCharts>(`${this.API_URL}/api/admin/dashboard/charts`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des graphiques admin:', error);
          return of(this.getDefaultCharts());
        })
      );
  }

  /**
   * Récupère les marketers récents
   */
  getRecentMarketers(limit: number = 10): Observable<RecentMarketerDto[]> {
    return this.http.get<RecentMarketerDto[]>(`${this.API_URL}/api/admin/dashboard/recent-marketers?limit=${limit}`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des marketers récents:', error);
          return of(this.getDefaultRecentMarketers());
        })
      );
  }

  /**
   * Récupère les commandes récentes
   */
  getRecentOrders(limit: number = 10): Observable<RecentOrderDto[]> {
    return this.http.get<RecentOrderDto[]>(`${this.API_URL}/api/admin/dashboard/recent-orders?limit=${limit}`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors du chargement des commandes récentes:', error);
          return of(this.getDefaultRecentOrders());
        })
      );
  }

  /**
   * Récupère les informations de l'administrateur connecté
   */
  getAdminInfo(): Observable<AdminInfoDto> {
    return this.http.get<AdminInfoDto>(`${this.API_URL}/api/admin/dashboard/profile`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors du chargement du profil admin:', error);
          return of(this.getDefaultAdminInfo());
        })
      );
  }

  /**
   * Actualise toutes les données du dashboard
   */
  refreshDashboardData(): Observable<{
    stats: AdminDashboardStats;
    charts: AdminDashboardCharts;
    recentMarketers: RecentMarketerDto[];
    recentOrders: RecentOrderDto[];
  }> {
    return this.http.get<{
      stats: AdminDashboardStats;
      charts: AdminDashboardCharts;
      recentMarketers: RecentMarketerDto[];
      recentOrders: RecentOrderDto[];
    }>(`${this.API_URL}/api/admin/dashboard/refresh`)
      .pipe(
        catchError((error) => {
          console.error('❌ Erreur lors de l\'actualisation du dashboard:', error);
          return of({
            stats: this.getDefaultStats(),
            charts: this.getDefaultCharts(),
            recentMarketers: this.getDefaultRecentMarketers(),
            recentOrders: this.getDefaultRecentOrders()
          });
        })
      );
  }

  // ==================== DONNÉES PAR DÉFAUT ====================

  private getDefaultStats(): AdminDashboardStats {
    return {
      // Marketers
      marketersEnAttente: 12,
      marketersActifs: 156,
      totalMarketers: 168,
      variationMarketersVsPrecedent: 8.5,

      // Commandes
      commandesNonTraitees: 23,
      commandesConfirmees: 89,
      commandesLivrees: 67,
      variationCommandesVsPrecedent: 12.3,

      // Produits
      produitsWowPriceActifs: 45,
      totalProduitsPromus: 234,
      variationProduitsVsPrecedent: 5.7,

      // Finances
      revenuBrutGenere: 45678.90,
      facturesImpayees: 12345.67,
      montantTotalFactures: 34567.89,
      variationRevenuVsPrecedent: 15.2,

      // Périodes
      periodeActuelle: 'Décembre 2024',
      periodePrecedente: 'Novembre 2024'
    };
  }

  private getDefaultCharts(): AdminDashboardCharts {
    console.warn('⚠️ Utilisation de données par défaut pour les graphiques - Vérifier la connexion backend');

    // Générer des données plus réalistes pour les 7 derniers jours
    const statutsCommandes = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      statutsCommandes.push({
        date: date.toISOString().substring(0, 10),
        confirmees: Math.floor(Math.random() * 15) + 5,
        rejetees: Math.floor(Math.random() * 3),
        enAttente: Math.floor(Math.random() * 8) + 2
      });
    }

    return {
      activiteMarketers: [
        { marketerId: 1, marketerName: 'Marketer 1', commandesSoumises: 25, commandesConfirmees: 20, tauxConversion: 80.0 },
        { marketerId: 2, marketerName: 'Marketer 2', commandesSoumises: 22, commandesConfirmees: 18, tauxConversion: 81.8 },
        { marketerId: 3, marketerName: 'Marketer 3', commandesSoumises: 18, commandesConfirmees: 15, tauxConversion: 83.3 },
        { marketerId: 4, marketerName: 'Marketer 4', commandesSoumises: 15, commandesConfirmees: 12, tauxConversion: 80.0 },
        { marketerId: 5, marketerName: 'Marketer 5', commandesSoumises: 12, commandesConfirmees: 10, tauxConversion: 83.3 }
      ],
      statutsCommandes: statutsCommandes,
      repartitionRevenus: [
        { categorie: 'WOW Price', montant: 25000.00, pourcentage: 100, couleur: '#664DC9' }
      ]
    };
  }

  private getDefaultRecentMarketers(): RecentMarketerDto[] {
    console.warn('⚠️ Utilisation de données par défaut pour les marketers récents - Vérifier la connexion backend');
    return [
      {
        id: 1,
        firstName: 'Marketer',
        lastName: '1',
        email: '<EMAIL>',
        status: 'ACTIVE',
        statusLabel: 'Activé',
        statusColor: 'success',
        registrationDate: new Date().toISOString().substring(0, 10),
        city: 'Casablanca'
      },
      {
        id: 2,
        firstName: 'Marketer',
        lastName: '2',
        email: '<EMAIL>',
        status: 'PENDING',
        statusLabel: 'En attente',
        statusColor: 'warning',
        registrationDate: new Date(Date.now() - 86400000).toISOString().substring(0, 10),
        city: 'Rabat'
      },
      {
        id: 3,
        firstName: 'Marketer',
        lastName: '3',
        email: '<EMAIL>',
        status: 'ACTIVE',
        statusLabel: 'Activé',
        statusColor: 'success',
        registrationDate: new Date(Date.now() - 172800000).toISOString().substring(0, 10),
        city: 'Marrakech'
      },
      {
        id: 4,
        firstName: 'Marketer',
        lastName: '4',
        email: '<EMAIL>',
        status: 'ACTIVE',
        statusLabel: 'Activé',
        statusColor: 'success',
        registrationDate: new Date(Date.now() - 259200000).toISOString().substring(0, 10),
        city: 'Fès'
      },
      {
        id: 5,
        firstName: 'Marketer',
        lastName: '5',
        email: '<EMAIL>',
        status: 'SUSPENDED',
        statusLabel: 'Suspendu',
        statusColor: 'danger',
        registrationDate: new Date(Date.now() - 345600000).toISOString().substring(0, 10),
        city: 'Tanger'
      }
    ];
  }

  private getDefaultRecentOrders(): RecentOrderDto[] {
    return [
      {
        id: 1,
        orderCode: 'CMD-2024-001',
        marketerName: 'Ahmed Ben Ali',
        marketerId: 1,
        totalAmount: 1250.00,
        status: 'CONFIRMED',
        statusLabel: 'Confirmée',
        statusColor: 'success',
        orderDate: '2024-12-07',
        customerName: 'Karim El Amrani',
        customerCity: 'Casablanca'
      },
      {
        id: 2,
        orderCode: 'CMD-2024-002',
        marketerName: 'Fatima Zahra',
        marketerId: 2,
        totalAmount: 890.50,
        status: 'PENDING',
        statusLabel: 'En attente',
        statusColor: 'warning',
        orderDate: '2024-12-07',
        customerName: 'Sara Bennani',
        customerCity: 'Rabat'
      },
      {
        id: 3,
        orderCode: 'CMD-2024-003',
        marketerName: 'Mohammed Alami',
        marketerId: 3,
        totalAmount: 2100.00,
        status: 'DELIVERED',
        statusLabel: 'Livrée',
        statusColor: 'primary',
        orderDate: '2024-12-06',
        customerName: 'Youssef Mansouri',
        customerCity: 'Marrakech'
      },
      {
        id: 4,
        orderCode: 'CMD-2024-004',
        marketerName: 'Amina Tazi',
        marketerId: 4,
        totalAmount: 750.25,
        status: 'CONFIRMED',
        statusLabel: 'Confirmée',
        statusColor: 'success',
        orderDate: '2024-12-06',
        customerName: 'Leila Berrada',
        customerCity: 'Fès'
      },
      {
        id: 5,
        orderCode: 'CMD-2024-005',
        marketerName: 'Hassan El Fassi',
        marketerId: 5,
        totalAmount: 1650.75,
        status: 'REJECTED',
        statusLabel: 'Rejetée',
        statusColor: 'danger',
        orderDate: '2024-12-05',
        customerName: 'Omar Tazi',
        customerCity: 'Tanger'
      }
    ];
  }

  private getDefaultAdminInfo(): AdminInfoDto {
    return {
      id: 1,
      firstName: 'Admin',
      lastName: 'Affilink',
      email: '<EMAIL>',
      role: 'ADMIN',
      permissions: ['READ', 'WRITE', 'DELETE', 'MANAGE_USERS', 'MANAGE_ORDERS']
    };
  }
}
