package com.forlivraison.web.service;

import com.forlivraison.web.dto.*;
import com.forlivraison.web.entity.*;
import com.forlivraison.web.dao.*;
import com.forlivraison.web.enums.OrderStatus;
import com.forlivraison.web.enums.StatutFacture;
import com.forlivraison.web.enums.UserStatus;
import com.forlivraison.web.enums.RoleName;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service métier pour le dashboard administrateur
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class AdminDashboardServiceImpl implements AdminDashboardService {

    // Repositories
    private final UserRepository userRepository;
    private final ProductRepository productRepository;
    private final OrderRepository orderRepository;
    private final CommissionRepository commissionRepository;
    private final FactureRepository factureRepository;
    private final MarketerProductSelectionRepository marketerProductSelectionRepository;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.FRENCH);

    @Override
    @Cacheable(value = "admin-dashboard-stats")
    public AdminDashboardStatsDto getDashboardStats() {
        log.info("📊 Calcul des statistiques admin - Début");

        // Périodes de calcul
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfCurrentMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startOfPreviousMonth = startOfCurrentMonth.minusMonths(1);
        LocalDateTime endOfPreviousMonth = startOfCurrentMonth.minusSeconds(1);

        log.info("📅 Période actuelle: {} à {}", startOfCurrentMonth, now);

        // 1. Statistiques des marketers
        Long marketersEnAttente = userRepository.countByStatusAndRoleName(UserStatus.PENDING, RoleName.MARKETER);
        Long marketersActifs = userRepository.countByStatusAndRoleName(UserStatus.ACTIVE, RoleName.MARKETER);
        Long totalMarketers = marketersEnAttente + marketersActifs;

        log.info("👥 Marketers - En attente: {}, Actifs: {}, Total: {}",
            marketersEnAttente, marketersActifs, totalMarketers);

        // 2. Statistiques des commandes
        Long commandesNonTraitees = orderRepository.countByStatus(OrderStatus.PENDING);
        Long commandesConfirmees = orderRepository.countByStatusAndDateRange(
            OrderStatus.CONFIRMED, startOfCurrentMonth, now);
        Long commandesLivrees = orderRepository.countByStatusAndDateRange(
            OrderStatus.DELIVERED, startOfCurrentMonth, now);

        log.info("📦 Commandes - Non traitées: {}, Confirmées: {}, Livrées: {}",
            commandesNonTraitees, commandesConfirmees, commandesLivrees);

        // 3. Statistiques des produits
        Long produitsWowPriceActifs = marketerProductSelectionRepository.countActiveWowPriceProducts();
        Long totalProduitsPromus = marketerProductSelectionRepository.countActiveProducts();

        log.info("🛍️ Produits - WOW Price actifs: {}, Total promus: {}",
            produitsWowPriceActifs, totalProduitsPromus);

        // 4. Statistiques financières
        BigDecimal revenuBrut = orderRepository.sumTotalAmountByDateRange(startOfCurrentMonth, now);
        BigDecimal facturesImpayees = factureRepository.sumAmountByStatus(StatutFacture.NON_PAYEE);
        BigDecimal montantTotalFactures = factureRepository.sumTotalAmount();

        log.info("💰 Finances - Revenu brut: {}, Factures impayées: {}, Total factures: {}",
            revenuBrut, facturesImpayees, montantTotalFactures);

        // 5. Calcul des variations (simplifié pour l'exemple)
        double variationMarketers = calculateVariation(marketersActifs, marketersActifs - 5); // Exemple
        double variationCommandes = calculateVariation(commandesConfirmees, commandesConfirmees - 10); // Exemple
        double variationProduits = calculateVariation(produitsWowPriceActifs, produitsWowPriceActifs - 2); // Exemple
        double variationRevenu = calculateVariation(revenuBrut, revenuBrut != null ? revenuBrut.subtract(BigDecimal.valueOf(5000)) : BigDecimal.ZERO); // Exemple

        return AdminDashboardStatsDto.builder()
                // Marketers
                .marketersEnAttente(marketersEnAttente.intValue())
                .marketersActifs(marketersActifs.intValue())
                .totalMarketers(totalMarketers.intValue())
                .variationMarketersVsPrecedent(variationMarketers)

                // Commandes
                .commandesNonTraitees(commandesNonTraitees.intValue())
                .commandesConfirmees(commandesConfirmees.intValue())
                .commandesLivrees(commandesLivrees.intValue())
                .variationCommandesVsPrecedent(variationCommandes)

                // Produits
                .produitsWowPriceActifs(produitsWowPriceActifs.intValue())
                .totalProduitsPromus(totalProduitsPromus.intValue())
                .variationProduitsVsPrecedent(variationProduits)

                // Finances
                .revenuBrutGenere(revenuBrut != null ? revenuBrut.doubleValue() : 0.0)
                .facturesImpayees(facturesImpayees != null ? facturesImpayees.doubleValue() : 0.0)
                .montantTotalFactures(montantTotalFactures != null ? montantTotalFactures.doubleValue() : 0.0)
                .variationRevenuVsPrecedent(variationRevenu)

                // Périodes
                .periodeActuelle(now.format(MONTH_FORMATTER))
                .periodePrecedente(startOfPreviousMonth.format(MONTH_FORMATTER))
                .build();
    }

    @Override
    @Cacheable(value = "admin-dashboard-charts")
    public AdminDashboardChartsDto getDashboardCharts() {
        log.info("📈 Calcul des données graphiques admin");

        // 1. Activité des marketers (Top 5)
        List<MarketerActivityDto> activiteMarketers = getTopMarketersActivity();

        // 2. Statuts des commandes (7 derniers jours)
        List<OrderStatusDto> statutsCommandes = getOrderStatusEvolution();

        // 3. Répartition des revenus
        List<RevenueCategoryDto> repartitionRevenus = getRevenueDistribution();

        return AdminDashboardChartsDto.builder()
                .activiteMarketers(activiteMarketers)
                .statutsCommandes(statutsCommandes)
                .repartitionRevenus(repartitionRevenus)
                .build();
    }

    @Override
    public List<RecentMarketerDto> getRecentMarketers(int limit) {
        log.info("👥 Récupération des {} marketers récents", limit);

        List<User> marketers = userRepository.findRecentMarketersByRole(
            RoleName.MARKETER, PageRequest.of(0, limit));

        return marketers.stream()
                .map(this::mapUserToRecentMarketerDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<RecentOrderDto> getRecentOrders(int limit) {
        log.info("📦 Récupération des {} commandes récentes", limit);

        List<Order> orders = orderRepository.findRecentOrders(PageRequest.of(0, limit));

        return orders.stream()
                .map(this::mapOrderToRecentOrderDto)
                .collect(Collectors.toList());
    }

    @Override
    public AdminInfoDto getAdminInfo(Long adminId) {
        log.info("👤 Récupération des informations admin ID: {}", adminId);

        User admin = userRepository.findById(adminId)
                .orElseThrow(() -> new RuntimeException("Administrateur non trouvé"));

        List<String> permissions = new ArrayList<>();
        if (admin.getRole() != null) {
            // Récupérer les permissions depuis le repository
            permissions = permissionRepository.findPermissionNamesByRoleId(admin.getRole().getId());
        }

        return AdminInfoDto.builder()
                .id(admin.getId())
                .firstName(admin.getFirstName())
                .lastName(admin.getLastName())
                .email(admin.getEmail())
                .role(admin.getRole() != null ? admin.getRole().getName().name() : "UNKNOWN")
                .permissions(permissions)
                .build();
    }

    @Override
    public Map<String, Object> refreshDashboardData() {
        log.info("🔄 Actualisation des données du dashboard admin");

        Map<String, Object> data = new HashMap<>();
        data.put("stats", getDashboardStats());
        data.put("charts", getDashboardCharts());
        data.put("recentMarketers", getRecentMarketers(10));
        data.put("recentOrders", getRecentOrders(10));

        return data;
    }

    // ==================== MÉTHODES PRIVÉES ====================

    private List<MarketerActivityDto> getTopMarketersActivity() {
        // Récupérer les top 5 marketers par nombre de commandes (30 derniers jours)
        LocalDateTime startDate = LocalDateTime.now().minusDays(30);
        List<Object[]> topMarketers = orderRepository.findTopMarketersByOrderCount(startDate, PageRequest.of(0, 5));

        return topMarketers.stream()
                .map(this::mapToMarketerActivityDto)
                .collect(Collectors.toList());
    }

    private List<OrderStatusDto> getOrderStatusEvolution() {
        // Récupérer les données des 7 derniers jours
        LocalDateTime startDate = LocalDateTime.now().minusDays(7);
        List<Object[]> evolutionData = orderRepository.findOrderStatusEvolution(startDate);

        List<OrderStatusDto> evolution = new ArrayList<>();
        LocalDate endDate = LocalDate.now();

        // Créer un map pour les données existantes
        Map<String, Object[]> dataMap = evolutionData.stream()
                .collect(Collectors.toMap(
                    data -> (String) data[0],
                    data -> data
                ));

        // Générer les 7 derniers jours
        for (int i = 6; i >= 0; i--) {
            LocalDate date = endDate.minusDays(i);
            String dateStr = date.format(DATE_FORMATTER);
            
            Object[] data = dataMap.get(dateStr);
            Integer confirmees = data != null ? ((Number) data[1]).intValue() : 0;
            Integer enAttente = data != null ? ((Number) data[2]).intValue() : 0;
            Integer livrees = data != null ? ((Number) data[3]).intValue() : 0;

            evolution.add(OrderStatusDto.builder()
                    .date(dateStr)
                    .confirmees(confirmees + livrees) // Combine confirmed and delivered
                    .rejetees(0) // No rejected status in current system
                    .enAttente(enAttente)
                    .build());
        }

        return evolution;
    }

    private List<RevenueCategoryDto> getRevenueDistribution() {
        // Pour l'instant, tout est WOW Price
        LocalDateTime startDate = LocalDateTime.now().minusDays(30);
        LocalDateTime endDate = LocalDateTime.now();
        BigDecimal totalRevenue = orderRepository.sumTotalAmountByDateRange(startDate, endDate);

        return Arrays.asList(RevenueCategoryDto.builder()
                .categorie("WOW Price")
                .montant(totalRevenue != null ? totalRevenue.doubleValue() : 0.0)
                .pourcentage(100.0)
                .couleur("#664DC9")
                .build());
    }

    private MarketerActivityDto mapToMarketerActivityDto(Object[] data) {
        Long marketerId = (Long) data[0];
        String marketerName = (String) data[1];
        Long commandesSoumises = (Long) data[2];
        Long commandesConfirmees = (Long) data[3];

        // Nettoyer le nom du marketer pour éviter les "null null"
        if (marketerName == null || marketerName.trim().isEmpty() ||
            marketerName.contains("null") || marketerName.equals(" ")) {
            marketerName = "Marketer " + marketerId;
        }

        double tauxConversion = 0.0;
        if (commandesSoumises > 0) {
            tauxConversion = (commandesConfirmees.doubleValue() / commandesSoumises.doubleValue()) * 100.0;
        }

        return MarketerActivityDto.builder()
                .marketerId(marketerId)
                .marketerName(marketerName.trim())
                .commandesSoumises(commandesSoumises.intValue())
                .commandesConfirmees(commandesConfirmees.intValue())
                .tauxConversion(tauxConversion)
                .build();
    }

    private RecentMarketerDto mapUserToRecentMarketerDto(User user) {
        // Nettoyer les noms pour éviter les valeurs null
        String firstName = user.getFirstName();
        String lastName = user.getLastName();

        if (firstName == null || firstName.trim().isEmpty()) {
            firstName = "Prénom";
        }
        if (lastName == null || lastName.trim().isEmpty()) {
            lastName = "Nom " + user.getId();
        }

        return RecentMarketerDto.builder()
                .id(user.getId())
                .firstName(firstName.trim())
                .lastName(lastName.trim())
                .email(user.getEmail() != null ? user.getEmail() : "email" + user.getId() + "@affilink.ma")
                .status(user.getStatus() != null ? user.getStatus().name() : "PENDING")
                .statusLabel(user.getStatus() != null ? getStatusLabel(user.getStatus()) : "En attente")
                .statusColor(user.getStatus() != null ? getStatusColor(user.getStatus()) : "warning")
                .registrationDate(user.getCreatedAt() != null ?
                    user.getCreatedAt().toString().substring(0, 10) :
                    LocalDateTime.now().toString().substring(0, 10))
                .city(user.getVille() != null ? user.getVille().getNom() : "Ville inconnue")
                .build();
    }

    private RecentOrderDto mapOrderToRecentOrderDto(Order order) {
        // Construire le nom du marketer de manière sécurisée
        String marketerName = "Marketer inconnu";
        if (order.getMarketer() != null) {
            String firstName = order.getMarketer().getFirstName();
            String lastName = order.getMarketer().getLastName();

            if (firstName != null && !firstName.trim().isEmpty() &&
                lastName != null && !lastName.trim().isEmpty()) {
                marketerName = firstName.trim() + " " + lastName.trim();
            } else {
                marketerName = "Marketer " + order.getMarketer().getId();
            }
        }

        return RecentOrderDto.builder()
                .id(order.getId())
                .orderCode("CMD-" + String.format("%06d", order.getId()))
                .marketerName(marketerName)
                .marketerId(order.getMarketer() != null ? order.getMarketer().getId() : null)
                .totalAmount(order.getTotalAmount() != null ? order.getTotalAmount().doubleValue() : 0.0)
                .status(order.getStatus() != null ? order.getStatus().name() : "PENDING")
                .statusLabel(order.getStatus() != null ? getOrderStatusLabel(order.getStatus()) : "En attente")
                .statusColor(order.getStatus() != null ? getOrderStatusColor(order.getStatus()) : "warning")
                .orderDate(order.getOrderDate() != null ?
                    order.getOrderDate().toString().substring(0, 10) :
                    LocalDateTime.now().toString().substring(0, 10))
                .customerName(order.getNomDestinataire() != null && !order.getNomDestinataire().trim().isEmpty() ?
                    order.getNomDestinataire() : "Client " + order.getId())
                .customerCity(order.getVilleDestination() != null ? order.getVilleDestination().getNom() : "Ville inconnue")
                .build();
    }

    private String getStatusLabel(UserStatus status) {
        switch (status) {
            case ACTIVE: return "Activé";
            case PENDING: return "En attente";
            case SUSPENDED: return "Suspendu";
            default: return status.name();
        }
    }

    private String getStatusColor(UserStatus status) {
        switch (status) {
            case ACTIVE: return "success";
            case PENDING: return "warning";
            case SUSPENDED: return "danger";
            default: return "secondary";
        }
    }

    private String getOrderStatusLabel(OrderStatus status) {
        switch (status) {
            case CONFIRMED: return "Confirmée";
            case PENDING: return "En attente";
            case DELIVERED: return "Livrée";
            default: return status.name();
        }
    }

    private String getOrderStatusColor(OrderStatus status) {
        switch (status) {
            case CONFIRMED:
            case DELIVERED: return "success";
            case PENDING: return "warning";
            default: return "secondary";
        }
    }

    private double calculateVariation(Number current, Number previous) {
        if (previous == null || previous.doubleValue() == 0) {
            return 0.0;
        }
        return ((current.doubleValue() - previous.doubleValue()) / previous.doubleValue()) * 100.0;
    }

    // Injection du repository des permissions (à adapter selon votre architecture)
    private final PermissionRepository permissionRepository;
} 