import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {TokenStorageService} from '../login/authentication/token-storage.service';

@Component({
    selector: 'app-dashboard-switcher',
    template: ''
})
export class DashboardSwitcherComponent implements OnInit {
    public roles!: string[];
    public role!: string;

    constructor(private router: Router, private tokenStorage: TokenStorageService) {
    }

    ngOnInit(): void {
        this.roles = this.tokenStorage.getAuthorities();
        this.role = this.roles[0];

        const clientRole = 'MARKETER';
        const adminRoles = ['ADMIN'];

        if (this.role === clientRole) { // check if the user is a marketer
            this.router.navigate(['dashboard-marketer']);
        } else {
            if (adminRoles.indexOf(this.role) != -1) { // check if the user is an admin
                this.router.navigate(['dashboard-admin']);
            }
        }
    }
}
